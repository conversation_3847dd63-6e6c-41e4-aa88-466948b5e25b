{"name": "anthropic-proxy", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "wrangler pages dev", "deploy": "pnpm run build && wrangler pages deploy", "deploy:prod": "pnpm run build && wrangler pages deploy --env production", "cf-typegen": "wrangler types --env-interface CloudflareBindings"}, "dependencies": {"@anthropic-ai/sdk": "^0.52.0", "hono": "^4.7.10"}, "devDependencies": {"@hono/vite-build": "^1.2.0", "@hono/vite-dev-server": "^0.18.2", "vite": "^6.1.1", "wrangler": "^4.4.0"}}